package dev.pigmomo.yhkit2025.ui.dialog.productmonitor

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringOperationType
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringPlanEntity
import dev.pigmomo.yhkit2025.data.model.productmonitor.MonitoringType
import dev.pigmomo.yhkit2025.service.productmonitor.MonitoringServiceManager
import dev.pigmomo.yhkit2025.ui.theme.dialogContainerColor
import kotlinx.coroutines.launch

/**
 * 编辑监控任务弹窗
 * @param plan 要编辑的监控计划
 * @param onDismiss 关闭弹窗回调
 * @param onTaskUpdated 任务更新成功回调
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EditMonitoringTaskDialog(
    plan: MonitoringPlanEntity,
    onDismiss: () -> Unit,
    onTaskUpdated: () -> Unit
) {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    
    // 状态管理
    var taskName by remember { mutableStateOf(plan.name) }
    var selectedType by remember { mutableStateOf(plan.type) }
    var selectedOperationType by remember { mutableStateOf(plan.operationType) }
    var intervalSeconds by remember { mutableStateOf(plan.intervalSeconds.toString()) }
    var scheduledConfig by remember { mutableStateOf(plan.scheduledConfig) }
    var productIds by remember { mutableStateOf(plan.productIds.toMutableList()) }
    var priority by remember { mutableStateOf(plan.priority.toString()) }
    var maxExecutions by remember { mutableStateOf(plan.maxExecutions.toString()) }
    var maxRetries by remember { mutableStateOf(plan.maxRetries.toString()) }
    var retryIntervalSeconds by remember { mutableStateOf(plan.retryIntervalSeconds.toString()) }
    var enableRetry by remember { mutableStateOf(plan.enableRetry) }
    var note by remember { mutableStateOf(plan.note) }
    var isEnabled by remember { mutableStateOf(plan.isEnabled) }
    var isUpdating by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf("") }

    AlertDialog(
        onDismissRequest = onDismiss,
        confirmButton = {
            Button(
                onClick = {
                    if (taskName.isBlank()) {
                        errorMessage = "请输入任务名称"
                        return@Button
                    }
                    
                    if (productIds.all { it.isBlank() } && selectedType == MonitoringType.PRODUCT_DETAIL) {
                        errorMessage = "商品详情监控需要至少一个商品ID"
                        return@Button
                    }
                    
                    scope.launch {
                        isUpdating = true
                        try {
                            val monitoringPlanRepository = MonitoringServiceManager.getMonitoringPlanRepository(context)
                            
                            val updatedPlan = plan.copy(
                                name = taskName,
                                type = selectedType,
                                productIds = productIds.filter { it.isNotBlank() },
                                operationType = selectedOperationType,
                                intervalSeconds = intervalSeconds.toIntOrNull() ?: 60,
                                scheduledConfig = scheduledConfig,
                                priority = priority.toIntOrNull() ?: 5,
                                maxExecutions = maxExecutions.toIntOrNull() ?: -1,
                                maxRetries = maxRetries.toIntOrNull() ?: 3,
                                retryIntervalSeconds = retryIntervalSeconds.toIntOrNull() ?: 30,
                                enableRetry = enableRetry,
                                note = note,
                                isEnabled = isEnabled
                            )
                            
                            val success = monitoringPlanRepository.updateMonitoringPlan(updatedPlan)
                            if (success) {
                                onTaskUpdated()
                                onDismiss()
                            } else {
                                errorMessage = "更新任务失败"
                            }
                        } catch (e: Exception) {
                            errorMessage = "更新任务失败: ${e.message}"
                        } finally {
                            isUpdating = false
                        }
                    }
                },
                enabled = !isUpdating
            ) {
                if (isUpdating) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp
                    )
                } else {
                    Text("保存")
                }
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        },
        title = {
            Text("编辑监控任务")
        },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 600.dp)
                    .verticalScroll(rememberScrollState()),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                // 错误信息显示
                if (errorMessage.isNotEmpty()) {
                    Card(
                        colors = CardDefaults.cardColors(containerColor = Color.Red.copy(alpha = 0.1f))
                    ) {
                        Text(
                            text = errorMessage,
                            color = Color.Red,
                            modifier = Modifier.padding(12.dp)
                        )
                    }
                }

                // 基本信息
                Card {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "基本信息",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )
                        
                        Spacer(modifier = Modifier.height(12.dp))
                        
                        // 任务名称
                        OutlinedTextField(
                            value = taskName,
                            onValueChange = { 
                                taskName = it
                                errorMessage = ""
                            },
                            label = { Text("任务名称") },
                            modifier = Modifier.fillMaxWidth(),
                            singleLine = true
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 监控类型
                        Text("监控类型", fontWeight = FontWeight.Medium)
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            MonitoringType.values().forEach { type ->
                                FilterChip(
                                    onClick = { selectedType = type },
                                    label = { 
                                        Text(when (type) {
                                            MonitoringType.CART -> "购物车监控"
                                            MonitoringType.PRODUCT_DETAIL -> "商品详情监控"
                                        })
                                    },
                                    selected = selectedType == type
                                )
                            }
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 启用状态
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Switch(
                                checked = isEnabled,
                                onCheckedChange = { isEnabled = it }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("启用任务")
                        }
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // 优先级
                        OutlinedTextField(
                            value = priority,
                            onValueChange = { priority = it },
                            label = { Text("优先级 (1-10)") },
                            modifier = Modifier.fillMaxWidth(),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            singleLine = true
                        )
                    }
                }

                // 操作配置
                Card {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "操作配置",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )

                        Spacer(modifier = Modifier.height(12.dp))

                        // 操作类型
                        Text("操作类型", fontWeight = FontWeight.Medium)
                        Column(
                            verticalArrangement = Arrangement.spacedBy(4.dp)
                        ) {
                            MonitoringOperationType.values().forEach { type ->
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    RadioButton(
                                        selected = selectedOperationType == type,
                                        onClick = { selectedOperationType = type }
                                    )
                                    Text(
                                        text = when (type) {
                                            MonitoringOperationType.INTERVAL -> "间隔监控"
                                            MonitoringOperationType.SCHEDULED -> "定时监控"
                                            MonitoringOperationType.MANUAL -> "手动监控"
                                        },
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }
                            }
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // 间隔配置
                        if (selectedOperationType == MonitoringOperationType.INTERVAL) {
                            OutlinedTextField(
                                value = intervalSeconds,
                                onValueChange = { intervalSeconds = it },
                                label = { Text("监控间隔 (秒)") },
                                modifier = Modifier.fillMaxWidth(),
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true
                            )
                        }

                        // 定时配置
                        if (selectedOperationType == MonitoringOperationType.SCHEDULED) {
                            OutlinedTextField(
                                value = scheduledConfig,
                                onValueChange = { scheduledConfig = it },
                                label = { Text("定时配置 (JSON格式)") },
                                modifier = Modifier.fillMaxWidth(),
                                maxLines = 3
                            )
                        }

                        Spacer(modifier = Modifier.height(8.dp))

                        // 最大执行次数
                        OutlinedTextField(
                            value = maxExecutions,
                            onValueChange = { maxExecutions = it },
                            label = { Text("最大执行次数 (-1为无限制)") },
                            modifier = Modifier.fillMaxWidth(),
                            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                            singleLine = true
                        )
                    }
                }

                // 商品配置
                if (selectedType == MonitoringType.PRODUCT_DETAIL) {
                    Card {
                        Column(modifier = Modifier.padding(16.dp)) {
                            Text(
                                text = "商品配置",
                                fontWeight = FontWeight.Bold,
                                fontSize = 16.sp
                            )

                            Spacer(modifier = Modifier.height(12.dp))

                            productIds.forEachIndexed { index, productId ->
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    OutlinedTextField(
                                        value = productId,
                                        onValueChange = { newValue ->
                                            productIds = productIds.toMutableList().apply {
                                                this[index] = newValue
                                            }
                                        },
                                        label = { Text("商品ID ${index + 1}") },
                                        modifier = Modifier.weight(1f),
                                        singleLine = true
                                    )

                                    if (productIds.size > 1) {
                                        TextButton(
                                            onClick = {
                                                productIds = productIds.toMutableList().apply {
                                                    removeAt(index)
                                                }
                                            }
                                        ) {
                                            Text("删除", color = Color.Red)
                                        }
                                    }
                                }

                                if (index < productIds.size - 1) {
                                    Spacer(modifier = Modifier.height(8.dp))
                                }
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            TextButton(
                                onClick = {
                                    productIds = productIds.toMutableList().apply {
                                        add("")
                                    }
                                }
                            ) {
                                Text("添加商品ID")
                            }
                        }
                    }
                }

                // 重试配置
                Card {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "重试配置",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )

                        Spacer(modifier = Modifier.height(12.dp))

                        // 启用重试
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Switch(
                                checked = enableRetry,
                                onCheckedChange = { enableRetry = it }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text("启用失败重试")
                        }

                        if (enableRetry) {
                            Spacer(modifier = Modifier.height(8.dp))

                            // 最大重试次数
                            OutlinedTextField(
                                value = maxRetries,
                                onValueChange = { maxRetries = it },
                                label = { Text("最大重试次数") },
                                modifier = Modifier.fillMaxWidth(),
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            // 重试间隔
                            OutlinedTextField(
                                value = retryIntervalSeconds,
                                onValueChange = { retryIntervalSeconds = it },
                                label = { Text("重试间隔 (秒)") },
                                modifier = Modifier.fillMaxWidth(),
                                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                                singleLine = true
                            )
                        }
                    }
                }

                // 备注
                Card {
                    Column(modifier = Modifier.padding(16.dp)) {
                        Text(
                            text = "备注信息",
                            fontWeight = FontWeight.Bold,
                            fontSize = 16.sp
                        )

                        Spacer(modifier = Modifier.height(12.dp))

                        OutlinedTextField(
                            value = note,
                            onValueChange = { note = it },
                            label = { Text("任务备注") },
                            modifier = Modifier.fillMaxWidth(),
                            maxLines = 3
                        )
                    }
                }
            }
        },
        containerColor = dialogContainerColor()
    )
}
